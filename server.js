#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';

/**
 * n8n MCP服务器
 * 提供n8n工作流自动化工具
 */
class N8nMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'n8n-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
    this.setupErrorHandling();
  }

  setupToolHandlers() {
    // 列出可用工具
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'create_workflow',
            description: '创建n8n工作流',
            inputSchema: {
              type: 'object',
              properties: {
                name: {
                  type: 'string',
                  description: '工作流名称',
                },
                description: {
                  type: 'string',
                  description: '工作流描述',
                },
                nodes: {
                  type: 'array',
                  description: '工作流节点配置',
                  items: {
                    type: 'object',
                    properties: {
                      type: { type: 'string' },
                      name: { type: 'string' },
                      parameters: { type: 'object' }
                    }
                  }
                }
              },
              required: ['name'],
            },
          },
          {
            name: 'execute_workflow',
            description: '执行n8n工作流',
            inputSchema: {
              type: 'object',
              properties: {
                workflowId: {
                  type: 'string',
                  description: '工作流ID',
                },
                data: {
                  type: 'object',
                  description: '输入数据',
                }
              },
              required: ['workflowId'],
            },
          },
          {
            name: 'list_workflows',
            description: '列出所有工作流',
            inputSchema: {
              type: 'object',
              properties: {
                active: {
                  type: 'boolean',
                  description: '是否只显示活跃的工作流',
                }
              },
            },
          },
          {
            name: 'get_workflow_status',
            description: '获取工作流执行状态',
            inputSchema: {
              type: 'object',
              properties: {
                executionId: {
                  type: 'string',
                  description: '执行ID',
                }
              },
              required: ['executionId'],
            },
          },
          {
            name: 'webhook_trigger',
            description: '创建Webhook触发器',
            inputSchema: {
              type: 'object',
              properties: {
                path: {
                  type: 'string',
                  description: 'Webhook路径',
                },
                method: {
                  type: 'string',
                  enum: ['GET', 'POST', 'PUT', 'DELETE'],
                  description: 'HTTP方法',
                }
              },
              required: ['path'],
            },
          }
        ],
      };
    });

    // 处理工具调用
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'create_workflow':
            return await this.createWorkflow(args);
          case 'execute_workflow':
            return await this.executeWorkflow(args);
          case 'list_workflows':
            return await this.listWorkflows(args);
          case 'get_workflow_status':
            return await this.getWorkflowStatus(args);
          case 'webhook_trigger':
            return await this.createWebhookTrigger(args);
          default:
            throw new Error(`未知工具: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `错误: ${error.message}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  async createWorkflow(args) {
    const { name, description, nodes = [] } = args;
    
    // 模拟创建工作流
    const workflowId = `workflow_${Date.now()}`;
    const workflow = {
      id: workflowId,
      name,
      description,
      nodes,
      active: false,
      createdAt: new Date().toISOString()
    };

    return {
      content: [
        {
          type: 'text',
          text: `✅ 工作流创建成功!\n\n工作流ID: ${workflowId}\n名称: ${name}\n描述: ${description || '无'}\n节点数量: ${nodes.length}\n状态: 未激活\n\n工作流详情:\n${JSON.stringify(workflow, null, 2)}`,
        },
      ],
    };
  }

  async executeWorkflow(args) {
    const { workflowId, data = {} } = args;
    
    // 模拟执行工作流
    const executionId = `exec_${Date.now()}`;
    const execution = {
      id: executionId,
      workflowId,
      status: 'running',
      startedAt: new Date().toISOString(),
      data
    };

    // 模拟执行完成
    setTimeout(() => {
      execution.status = 'success';
      execution.finishedAt = new Date().toISOString();
    }, 1000);

    return {
      content: [
        {
          type: 'text',
          text: `🚀 工作流执行已启动!\n\n执行ID: ${executionId}\n工作流ID: ${workflowId}\n状态: 运行中\n开始时间: ${execution.startedAt}\n\n输入数据:\n${JSON.stringify(data, null, 2)}`,
        },
      ],
    };
  }

  async listWorkflows(args) {
    const { active } = args;
    
    // 模拟工作流列表
    const workflows = [
      {
        id: 'workflow_1',
        name: '邮件通知工作流',
        description: '发送邮件通知',
        active: true,
        lastExecution: '2024-09-24T10:30:00Z'
      },
      {
        id: 'workflow_2', 
        name: '数据同步工作流',
        description: '同步数据库数据',
        active: false,
        lastExecution: '2024-09-23T15:45:00Z'
      },
      {
        id: 'workflow_3',
        name: 'API集成工作流',
        description: '第三方API集成',
        active: true,
        lastExecution: '2024-09-24T09:15:00Z'
      }
    ];

    const filteredWorkflows = active !== undefined 
      ? workflows.filter(w => w.active === active)
      : workflows;

    return {
      content: [
        {
          type: 'text',
          text: `📋 工作流列表 (${filteredWorkflows.length}个):\n\n${filteredWorkflows.map(w => 
            `• ${w.name} (${w.id})\n  描述: ${w.description}\n  状态: ${w.active ? '✅ 活跃' : '⏸️ 未激活'}\n  最后执行: ${w.lastExecution}\n`
          ).join('\n')}`,
        },
      ],
    };
  }

  async getWorkflowStatus(args) {
    const { executionId } = args;
    
    // 模拟执行状态
    const status = {
      id: executionId,
      status: 'success',
      startedAt: '2024-09-24T10:00:00Z',
      finishedAt: '2024-09-24T10:01:30Z',
      duration: 90000, // 90秒
      nodesExecuted: 5,
      success: true
    };

    return {
      content: [
        {
          type: 'text',
          text: `📊 执行状态:\n\n执行ID: ${executionId}\n状态: ${status.success ? '✅ 成功' : '❌ 失败'}\n开始时间: ${status.startedAt}\n结束时间: ${status.finishedAt}\n执行时长: ${status.duration/1000}秒\n执行节点数: ${status.nodesExecuted}`,
        },
      ],
    };
  }

  async createWebhookTrigger(args) {
    const { path, method = 'POST' } = args;
    
    // 模拟创建Webhook
    const webhookUrl = `https://your-n8n-instance.com/webhook/${path}`;
    const webhook = {
      id: `webhook_${Date.now()}`,
      path,
      method,
      url: webhookUrl,
      active: true,
      createdAt: new Date().toISOString()
    };

    return {
      content: [
        {
          type: 'text',
          text: `🔗 Webhook创建成功!\n\nWebhook ID: ${webhook.id}\n路径: ${path}\nHTTP方法: ${method}\nURL: ${webhookUrl}\n状态: 活跃\n创建时间: ${webhook.createdAt}\n\n使用方法:\ncurl -X ${method} "${webhookUrl}" -H "Content-Type: application/json" -d '{"key": "value"}'`,
        },
      ],
    };
  }

  setupErrorHandling() {
    this.server.onerror = (error) => {
      console.error('[MCP Error]', error);
    };

    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('n8n MCP服务器已启动');
  }
}

// 启动服务器
const server = new N8nMCPServer();
server.run().catch(console.error);
