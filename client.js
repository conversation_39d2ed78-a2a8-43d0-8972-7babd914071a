#!/usr/bin/env node

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { spawn } from 'child_process';

/**
 * n8n MCP客户端示例
 * 演示如何调用n8n MCP服务器的工具
 */
class N8nMCPClient {
  constructor() {
    this.client = null;
  }

  async connect() {
    // 启动MCP服务器进程
    const serverProcess = spawn('node', ['server.js'], {
      stdio: ['pipe', 'pipe', 'inherit'],
    });

    // 创建传输层
    const transport = new StdioClientTransport({
      reader: serverProcess.stdout,
      writer: serverProcess.stdin,
    });

    // 创建客户端并连接
    this.client = new Client(
      {
        name: 'n8n-mcp-client',
        version: '1.0.0',
      },
      {
        capabilities: {},
      }
    );

    await this.client.connect(transport);
    console.log('✅ 已连接到n8n MCP服务器');

    return serverProcess;
  }

  async listTools() {
    console.log('\n📋 获取可用工具列表...');
    const response = await this.client.request(
      { method: 'tools/list' },
      {}
    );
    
    console.log('\n可用工具:');
    response.tools.forEach((tool, index) => {
      console.log(`${index + 1}. ${tool.name}`);
      console.log(`   描述: ${tool.description}`);
      console.log(`   参数: ${JSON.stringify(tool.inputSchema.properties, null, 2)}`);
      console.log('');
    });

    return response.tools;
  }

  async createWorkflow() {
    console.log('\n🔧 创建新工作流...');
    
    const workflowData = {
      name: '测试工作流',
      description: '这是一个测试工作流，用于演示MCP调用',
      nodes: [
        {
          type: 'webhook',
          name: 'Webhook触发器',
          parameters: {
            path: 'test-webhook',
            method: 'POST'
          }
        },
        {
          type: 'function',
          name: '数据处理',
          parameters: {
            code: 'return { message: "Hello from n8n!", data: $input.all() };'
          }
        },
        {
          type: 'email',
          name: '发送邮件',
          parameters: {
            to: '<EMAIL>',
            subject: '工作流执行通知'
          }
        }
      ]
    };

    const response = await this.client.request(
      { method: 'tools/call' },
      {
        name: 'create_workflow',
        arguments: workflowData
      }
    );

    console.log(response.content[0].text);
    return response;
  }

  async listWorkflows() {
    console.log('\n📋 获取工作流列表...');
    
    const response = await this.client.request(
      { method: 'tools/call' },
      {
        name: 'list_workflows',
        arguments: {}
      }
    );

    console.log(response.content[0].text);
    return response;
  }

  async executeWorkflow() {
    console.log('\n🚀 执行工作流...');
    
    const response = await this.client.request(
      { method: 'tools/call' },
      {
        name: 'execute_workflow',
        arguments: {
          workflowId: 'workflow_1',
          data: {
            user: 'test_user',
            action: 'login',
            timestamp: new Date().toISOString()
          }
        }
      }
    );

    console.log(response.content[0].text);
    return response;
  }

  async getWorkflowStatus() {
    console.log('\n📊 获取执行状态...');
    
    const response = await this.client.request(
      { method: 'tools/call' },
      {
        name: 'get_workflow_status',
        arguments: {
          executionId: 'exec_1727200800000'
        }
      }
    );

    console.log(response.content[0].text);
    return response;
  }

  async createWebhook() {
    console.log('\n🔗 创建Webhook触发器...');
    
    const response = await this.client.request(
      { method: 'tools/call' },
      {
        name: 'webhook_trigger',
        arguments: {
          path: 'api/notifications',
          method: 'POST'
        }
      }
    );

    console.log(response.content[0].text);
    return response;
  }

  async runDemo() {
    try {
      // 连接到服务器
      const serverProcess = await this.connect();

      // 演示各种MCP调用
      await this.listTools();
      await this.createWorkflow();
      await this.listWorkflows();
      await this.executeWorkflow();
      await this.getWorkflowStatus();
      await this.createWebhook();

      console.log('\n✅ 演示完成!');

      // 清理
      serverProcess.kill();
      await this.client.close();

    } catch (error) {
      console.error('❌ 错误:', error.message);
    }
  }
}

// 运行演示
const client = new N8nMCPClient();
client.runDemo();
